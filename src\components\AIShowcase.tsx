import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Star, DollarSign } from "lucide-react";

const AIShowcase = () => {
  const domainExample = {
    name: "techstartup.ai",
    price: "$15,000",
    score: 92,
    metrics: [
      { label: "Brand Score", value: 95, color: "text-green-600" },
      { label: "SEO Value", value: 88, color: "text-blue-600" },
      { label: "Market Demand", value: 94, color: "text-purple-600" }
    ],
    insights: [
      "High demand in AI/tech sector",
      "Premium .ai extension trending",
      "Short, memorable, brandable",
      "Similar domains sold for $10K-$25K"
    ]
  };

  return (
    <section className="py-20">
      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge variant="outline" className="w-fit">
                <Star className="w-3 h-3 mr-1" />
                AI Evaluation Engine
              </Badge>
              <h2 className="text-3xl lg:text-5xl font-bold">
                See the 
                <span className="bg-gradient-primary bg-clip-text text-transparent"> AI Magic</span> 
                <br />in Action
              </h2>
              <p className="text-xl text-muted-foreground">
                Our AI analyzes hundreds of factors including market trends, comparable sales, 
                SEO metrics, and brand potential to give you accurate valuations in seconds.
              </p>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <TrendingUp className="h-5 w-5 text-primary" />
                <span className="font-medium">Real-time market analysis</span>
              </div>
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-primary" />
                <span className="font-medium">Comparable sales data</span>
              </div>
              <div className="flex items-center gap-3">
                <Star className="h-5 w-5 text-primary" />
                <span className="font-medium">Brand value assessment</span>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <Card className="relative z-10 shadow-glow">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{domainExample.name}</CardTitle>
                  <Badge className="bg-gradient-primary text-primary-foreground">
                    Score: {domainExample.score}/100
                  </Badge>
                </div>
                <div className="text-2xl font-bold text-primary">{domainExample.price}</div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  {domainExample.metrics.map((metric, index) => (
                    <div key={index} className="flex items-center justify-between group/metric hover:bg-muted/30 rounded-lg p-2 -m-2 transition-all duration-300">
                      <span className="text-sm font-medium group-hover/metric:text-primary transition-colors duration-300">{metric.label}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded-full overflow-hidden group-hover/metric:scale-105 transition-transform duration-300">
                          <div 
                            className={`h-full bg-gradient-primary rounded-full transition-all duration-1000 animate-pulse`}
                            style={{ 
                              width: `${metric.value}%`,
                              animationDelay: `${index * 300}ms`
                            }}
                          ></div>
                        </div>
                        <span className={`text-sm font-medium ${metric.color} group-hover/metric:scale-110 transition-transform duration-300`}>
                          {metric.value}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="border-t pt-4">
                  <h4 className="font-medium mb-3">AI Insights</h4>
                  <ul className="space-y-2">
                    {domainExample.insights.map((insight, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-muted-foreground">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                        {insight}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
            
            {/* Floating elements for visual interest */}
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-primary/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-6 -left-6 w-20 h-20 bg-gradient-primary/10 rounded-full blur-2xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIShowcase;