import React, { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Search, Filter, Eye, EyeOff, TrendingUp, Calendar, BarChart3, X, Star, Globe, Users, Clock, DollarSign } from "lucide-react";
import { mockSearchResults } from "@/data/mockData";

const DomainSearch = () => {
  const { isAuthenticated } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [results, setResults] = useState(mockSearchResults);
  const [filteredResults, setFilteredResults] = useState(mockSearchResults);
  const [selectedDomain, setSelectedDomain] = useState<any>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: "all",
    priceRange: "all",
    scoreMin: 0,
    sortBy: "score"
  });

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // Search and filter logic
  const handleSearch = () => {
    let filtered = results;

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(domain =>
        domain.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        domain.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (filters.category !== "all") {
      filtered = filtered.filter(domain =>
        domain.category.toLowerCase() === filters.category.toLowerCase()
      );
    }

    // Filter by price range
    if (filters.priceRange !== "all") {
      const [min, max] = filters.priceRange.split("-").map(Number);
      filtered = filtered.filter(domain => {
        if (max) {
          return domain.price >= min && domain.price <= max;
        } else {
          return domain.price >= min;
        }
      });
    }

    // Filter by minimum score
    if (filters.scoreMin > 0) {
      filtered = filtered.filter(domain => domain.score >= filters.scoreMin);
    }

    // Sort results
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case "price-low":
          return a.price - b.price;
        case "price-high":
          return b.price - a.price;
        case "traffic":
          return b.traffic - a.traffic;
        case "expiry":
          return new Date(a.expires).getTime() - new Date(b.expires).getTime();
        case "score":
        default:
          return b.score - a.score;
      }
    });

    setFilteredResults(filtered);
  };

  // Trigger search when query or filters change
  React.useEffect(() => {
    handleSearch();
  }, [searchQuery, filters, results]);

  const toggleWatchlist = (domainId: string) => {
    const updatedResults = results.map(result =>
      result.id === domainId
        ? { ...result, isWatching: !result.isWatching }
        : result
    );
    setResults(updatedResults);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "bg-green-500";
    if (score >= 80) return "bg-blue-500";
    if (score >= 70) return "bg-yellow-500";
    return "bg-gray-500";
  };

  const getStatusColor = (expires: string) => {
    const daysUntilExpiry = Math.ceil((new Date(expires).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    if (daysUntilExpiry <= 7) return "text-red-600";
    if (daysUntilExpiry <= 30) return "text-yellow-600";
    return "text-green-600";
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <header className="h-16 flex items-center justify-between border-b px-6 bg-card/50 backdrop-blur-sm">
            <div className="flex items-center gap-4">
              <SidebarTrigger />
              <div className="hidden md:block">
                <h2 className="text-lg font-semibold text-foreground">Domain Discovery</h2>
                <p className="text-sm text-muted-foreground">Find and evaluate premium domains</p>
              </div>
            </div>
          </header>

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">Domain Discovery</h1>
                <p className="text-muted-foreground">
                  Find and evaluate premium domains with AI-powered insights
                </p>
              </div>
              <Button className="bg-gradient-primary">
                <TrendingUp className="mr-2 h-4 w-4" />
                Market Trends
              </Button>
            </div>

            {/* Search Section */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Search Domains</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search domains by keyword, category, or extension..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                      onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                    />
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setShowFilters(!showFilters)}
                    className={showFilters ? "bg-muted" : ""}
                  >
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                  </Button>
                  <Button onClick={handleSearch} className="bg-gradient-primary">
                    <Search className="mr-2 h-4 w-4" />
                    Search
                  </Button>
                </div>

                {/* Advanced Filters */}
                {showFilters && (
                  <div className="grid md:grid-cols-4 gap-4 p-4 bg-muted/30 rounded-lg">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Category</label>
                      <Select value={filters.category} onValueChange={(value) => setFilters({ ...filters, category: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="ai">AI & Tech</SelectItem>
                          <SelectItem value="finance">Finance</SelectItem>
                          <SelectItem value="ecommerce">E-commerce</SelectItem>
                          <SelectItem value="health">Health</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Price Range</label>
                      <Select value={filters.priceRange} onValueChange={(value) => setFilters({ ...filters, priceRange: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Any Price</SelectItem>
                          <SelectItem value="0-5000">$0 - $5,000</SelectItem>
                          <SelectItem value="5000-15000">$5,000 - $15,000</SelectItem>
                          <SelectItem value="15000-50000">$15,000 - $50,000</SelectItem>
                          <SelectItem value="50000">$50,000+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Min AI Score</label>
                      <Select value={filters.scoreMin.toString()} onValueChange={(value) => setFilters({ ...filters, scoreMin: parseInt(value) })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">Any Score</SelectItem>
                          <SelectItem value="70">70+</SelectItem>
                          <SelectItem value="80">80+</SelectItem>
                          <SelectItem value="90">90+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Sort By</label>
                      <Select value={filters.sortBy} onValueChange={(value) => setFilters({ ...filters, sortBy: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="score">AI Score</SelectItem>
                          <SelectItem value="price-low">Price (Low to High)</SelectItem>
                          <SelectItem value="price-high">Price (High to Low)</SelectItem>
                          <SelectItem value="traffic">Traffic</SelectItem>
                          <SelectItem value="expiry">Expiry Date</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {/* Quick Filters */}
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => setFilters({ ...filters, category: "ai" })}
                  >
                    AI Domains
                  </Badge>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => setFilters({ ...filters, category: "tech" })}
                  >
                    Tech Startups
                  </Badge>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => setFilters({ ...filters, category: "finance" })}
                  >
                    Finance
                  </Badge>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => setFilters({ ...filters, category: "ecommerce" })}
                  >
                    E-commerce
                  </Badge>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => setFilters({ ...filters, scoreMin: 90 })}
                  >
                    High Score
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Search Results */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Available Domains ({filteredResults.length})</h2>
                <div className="text-sm text-muted-foreground">
                  {filteredResults.length} of {results.length} domains
                </div>
              </div>

              <div className="grid gap-4">
                {filteredResults.map((domain) => (
                  <Card key={domain.id} className="border-0 shadow-soft hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-3">
                              <h3 className="text-lg font-semibold">{domain.name}</h3>
                              <Badge className={`text-white ${getScoreColor(domain.score)}`}>
                                {domain.score}
                              </Badge>
                              <Badge variant="outline">{domain.category}</Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <BarChart3 className="h-4 w-4" />
                                {domain.traffic.toLocaleString()} visits/mo
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                <span className={getStatusColor(domain.expires)}>
                                  Expires {new Date(domain.expires).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <div className="text-2xl font-bold">
                              ${domain.price.toLocaleString()}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Market Value
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => toggleWatchlist(domain.id)}
                            >
                              {domain.isWatching ? (
                                <><EyeOff className="h-4 w-4 mr-1" /> Unwatch</>
                              ) : (
                                <><Eye className="h-4 w-4 mr-1" /> Watch</>
                              )}
                            </Button>
                            <Button
                              size="sm"
                              className="bg-gradient-primary"
                              onClick={() => setSelectedDomain(domain)}
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Market Insights */}
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6 text-center">
                  <div className="text-2xl font-bold text-green-600">+12.5%</div>
                  <div className="text-sm text-muted-foreground">AI Domain Growth</div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6 text-center">
                  <div className="text-2xl font-bold text-blue-600">$8.2K</div>
                  <div className="text-sm text-muted-foreground">Avg. Tech Domain</div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6 text-center">
                  <div className="text-2xl font-bold text-purple-600">94%</div>
                  <div className="text-sm text-muted-foreground">Success Rate</div>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>

      {/* Domain Details Modal */}
      <Dialog open={!!selectedDomain} onOpenChange={() => setSelectedDomain(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedDomain && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-3">
                  <Globe className="h-6 w-6 text-primary" />
                  {selectedDomain.name}
                  <Badge className={`text-white ${getScoreColor(selectedDomain.score)}`}>
                    AI Score: {selectedDomain.score}
                  </Badge>
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-6">
                {/* Overview */}
                <div className="grid md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5" />
                        Valuation
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-primary">
                          ${selectedDomain.price.toLocaleString()}
                        </div>
                        <p className="text-muted-foreground">Market Value</p>
                      </div>
                      <Separator />
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Domain Length</span>
                          <span className="text-sm font-medium">{selectedDomain.name.length} chars</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Extension</span>
                          <span className="text-sm font-medium">.{selectedDomain.name.split('.').pop()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Category</span>
                          <Badge variant="outline">{selectedDomain.category}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Traffic & Analytics
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Monthly Visitors</span>
                          <span className="text-sm font-bold">{selectedDomain.traffic.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Domain Age</span>
                          <span className="text-sm font-medium">3 years</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Backlinks</span>
                          <span className="text-sm font-medium">1,250</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">SEO Score</span>
                          <div className="flex items-center gap-2">
                            <Progress value={selectedDomain.score} className="w-16" />
                            <span className="text-sm font-medium">{selectedDomain.score}/100</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* AI Analysis */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star className="h-5 w-5" />
                      AI-Powered Analysis
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">92%</div>
                        <div className="text-sm text-green-700">Brandability</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">88%</div>
                        <div className="text-sm text-blue-700">Memorability</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">95%</div>
                        <div className="text-sm text-purple-700">Market Potential</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-semibold">Key Insights:</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                          Strong keyword relevance in the {selectedDomain.category.toLowerCase()} sector
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                          High search volume for related terms (12K+ monthly searches)
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                          Premium extension with strong commercial appeal
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                          Growing market trend in this category (+15% YoY)
                        </li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                {/* Domain Details */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Domain Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Registration Date</span>
                          <span className="text-sm font-medium">Jan 15, 2021</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Expiry Date</span>
                          <span className={`text-sm font-medium ${getStatusColor(selectedDomain.expires)}`}>
                            {new Date(selectedDomain.expires).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Registrar</span>
                          <span className="text-sm font-medium">GoDaddy</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">DNS Status</span>
                          <Badge variant="outline" className="text-green-600 border-green-600">Active</Badge>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">SSL Certificate</span>
                          <Badge variant="outline" className="text-green-600 border-green-600">Valid</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">WHOIS Privacy</span>
                          <Badge variant="outline">Protected</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Transfer Lock</span>
                          <Badge variant="outline" className="text-red-600 border-red-600">Enabled</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Parking Status</span>
                          <Badge variant="outline">Not Parked</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => toggleWatchlist(selectedDomain.id)}
                    variant="outline"
                    className="flex-1"
                  >
                    {selectedDomain.isWatching ? (
                      <><EyeOff className="mr-2 h-4 w-4" /> Remove from Watchlist</>
                    ) : (
                      <><Eye className="mr-2 h-4 w-4" /> Add to Watchlist</>
                    )}
                  </Button>
                  <Button className="flex-1 bg-gradient-primary">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Make Offer
                  </Button>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </SidebarProvider>
  );
};

export default DomainSearch;