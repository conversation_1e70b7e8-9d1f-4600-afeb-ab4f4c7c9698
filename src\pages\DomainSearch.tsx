import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, Eye, EyeOff, TrendingUp, Calendar, BarChart3 } from "lucide-react";
import { mockSearchResults } from "@/data/mockData";

const DomainSearch = () => {
  const { isAuthenticated } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [results, setResults] = useState(mockSearchResults);
  const [filters, setFilters] = useState({
    category: "all",
    priceRange: "all",
    scoreMin: 0
  });

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const toggleWatchlist = (domainId: string) => {
    setResults(results.map(result =>
      result.id === domainId
        ? { ...result, isWatching: !result.isWatching }
        : result
    ));
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "bg-green-500";
    if (score >= 80) return "bg-blue-500";
    if (score >= 70) return "bg-yellow-500";
    return "bg-gray-500";
  };

  const getStatusColor = (expires: string) => {
    const daysUntilExpiry = Math.ceil((new Date(expires).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    if (daysUntilExpiry <= 7) return "text-red-600";
    if (daysUntilExpiry <= 30) return "text-yellow-600";
    return "text-green-600";
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <header className="h-12 flex items-center border-b px-6">
            <SidebarTrigger />
          </header>

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">Domain Discovery</h1>
                <p className="text-muted-foreground">
                  Find and evaluate premium domains with AI-powered insights
                </p>
              </div>
              <Button className="bg-gradient-primary">
                <TrendingUp className="mr-2 h-4 w-4" />
                Market Trends
              </Button>
            </div>

            {/* Search Section */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Search Domains</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search domains by keyword, category, or extension..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Advanced Filters
                  </Button>
                  <Button className="bg-gradient-primary">
                    Search
                  </Button>
                </div>

                {/* Quick Filters */}
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="cursor-pointer hover:bg-muted">AI Domains</Badge>
                  <Badge variant="outline" className="cursor-pointer hover:bg-muted">Tech Startups</Badge>
                  <Badge variant="outline" className="cursor-pointer hover:bg-muted">Finance</Badge>
                  <Badge variant="outline" className="cursor-pointer hover:bg-muted">E-commerce</Badge>
                  <Badge variant="outline" className="cursor-pointer hover:bg-muted">High Traffic</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Search Results */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Available Domains ({results.length})</h2>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Sort by:</span>
                  <select className="bg-background border rounded-md px-3 py-1 text-sm">
                    <option>AI Score</option>
                    <option>Price</option>
                    <option>Traffic</option>
                    <option>Expiry Date</option>
                  </select>
                </div>
              </div>

              <div className="grid gap-4">
                {results.map((domain) => (
                  <Card key={domain.id} className="border-0 shadow-soft hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-3">
                              <h3 className="text-lg font-semibold">{domain.name}</h3>
                              <Badge className={`text-white ${getScoreColor(domain.score)}`}>
                                {domain.score}
                              </Badge>
                              <Badge variant="outline">{domain.category}</Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <BarChart3 className="h-4 w-4" />
                                {domain.traffic.toLocaleString()} visits/mo
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                <span className={getStatusColor(domain.expires)}>
                                  Expires {new Date(domain.expires).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <div className="text-2xl font-bold">
                              ${domain.price.toLocaleString()}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Market Value
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => toggleWatchlist(domain.id)}
                            >
                              {domain.isWatching ? (
                                <><EyeOff className="h-4 w-4 mr-1" /> Unwatch</>
                              ) : (
                                <><Eye className="h-4 w-4 mr-1" /> Watch</>
                              )}
                            </Button>
                            <Button size="sm" className="bg-gradient-primary">
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Market Insights */}
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6 text-center">
                  <div className="text-2xl font-bold text-green-600">+12.5%</div>
                  <div className="text-sm text-muted-foreground">AI Domain Growth</div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6 text-center">
                  <div className="text-2xl font-bold text-blue-600">$8.2K</div>
                  <div className="text-sm text-muted-foreground">Avg. Tech Domain</div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6 text-center">
                  <div className="text-2xl font-bold text-purple-600">94%</div>
                  <div className="text-sm text-muted-foreground">Success Rate</div>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default DomainSearch;