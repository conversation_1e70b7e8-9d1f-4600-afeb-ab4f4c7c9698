import { Card, CardContent } from "@/components/ui/card";
import { Users, Target, TrendingUp, Award } from "lucide-react";

const Stats = () => {
  const stats = [
    {
      icon: <Users className="h-8 w-8 text-primary" />,
      number: "10,000+",
      label: "Active Investors",
      description: "Join a thriving community of domain investors"
    },
    {
      icon: <Target className="h-8 w-8 text-primary" />,
      number: "95%",
      label: "Success Rate",
      description: "Of domains sold within 90 days using our platform"
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-primary" />,
      number: "340%",
      label: "Average ROI",
      description: "Return on investment for our top performers"
    },
    {
      icon: <Award className="h-8 w-8 text-primary" />,
      number: "$50M+",
      label: "Total Sales",
      description: "Facilitated through our marketplace"
    }
  ];

  return (
    <section className="py-20 bg-gradient-hero">
      <div className="container mx-auto px-6">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl lg:text-5xl font-bold">
            Trusted by 
            <span className="bg-gradient-primary bg-clip-text text-transparent"> Investors</span> 
            <br />Worldwide
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join thousands of successful domain investors who trust our AI-powered platform 
            to maximize their returns.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-soft hover:scale-105 transition-all duration-500 border-0 bg-card/50 backdrop-blur cursor-pointer animate-fade-in"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 rounded-xl bg-gradient-secondary mx-auto flex items-center justify-center group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                  {stat.icon}
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300 inline-block">
                    {stat.number}
                  </div>
                  <div className="font-semibold text-foreground group-hover:text-primary transition-colors duration-300">{stat.label}</div>
                  <p className="text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">{stat.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Stats;