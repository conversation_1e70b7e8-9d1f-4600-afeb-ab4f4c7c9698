import { Card, CardContent } from "@/components/ui/card";
import { Search, Brain, Handshake } from "lucide-react";
import discoveryIcon from "@/assets/domain-discovery-icon.jpg";
import evaluationIcon from "@/assets/ai-evaluation-icon.jpg";
import resellIcon from "@/assets/domain-resell-icon.jpg";

const Features = () => {
  const features = [
    {
      icon: <Search className="h-8 w-8 text-primary" />,
      image: discoveryIcon,
      title: "Smart Discovery",
      description: "AI-powered algorithms scan millions of expiring domains and identify hidden gems with high investment potential.",
      details: [
        "Real-time monitoring of domain marketplaces",
        "Advanced filtering by industry and keywords",
        "Trending domain pattern analysis"
      ]
    },
    {
      icon: <Brain className="h-8 w-8 text-primary" />,
      image: evaluationIcon,
      title: "AI Evaluation",
      description: "Get instant, accurate valuations using machine learning models trained on millions of domain sales.",
      details: [
        "Market trend analysis and price predictions",
        "SEO value and traffic potential scoring",
        "Brand value and memorability assessment"
      ]
    },
    {
      icon: <Handshake className="h-8 w-8 text-primary" />,
      image: resellIcon,
      title: "Buyer Matching",
      description: "Connect with the right buyers through our intelligent matching system and automated outreach tools.",
      details: [
        "AI-powered buyer identification",
        "Automated personalized outreach campaigns",
        "Negotiation support and deal tracking"
      ]
    }
  ];

  return (
    <section className="py-20 bg-secondary/20">
      <div className="container mx-auto px-6">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl lg:text-5xl font-bold">
            Everything You Need to 
            <span className="bg-gradient-primary bg-clip-text text-transparent"> Succeed</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            From discovery to sale, our platform handles every aspect of domain investing 
            with the power of artificial intelligence.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-glow hover:scale-105 transition-all duration-500 border-0 bg-card/50 backdrop-blur cursor-pointer animate-fade-in"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <CardContent className="p-8 space-y-6">
                <div className="relative">
                  <div className="w-16 h-16 rounded-xl bg-gradient-secondary flex items-center justify-center mb-4 group-hover:rotate-6 transition-transform duration-300">
                    <img 
                      src={feature.image} 
                      alt={feature.title} 
                      className="w-10 h-10 object-cover rounded-lg group-hover:scale-110 transition-transform duration-300" 
                    />
                  </div>
                  <div className="absolute -top-2 -right-2 group-hover:scale-125 transition-transform duration-300">
                    {feature.icon}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold group-hover:text-primary transition-colors duration-300">{feature.title}</h3>
                  <p className="text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">{feature.description}</p>
                  
                  <ul className="space-y-2">
                    {feature.details.map((detail, idx) => (
                      <li key={idx} className="flex items-start gap-2 text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0 group-hover:scale-150 transition-transform duration-300"></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;