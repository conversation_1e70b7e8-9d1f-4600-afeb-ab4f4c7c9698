import { useState } from "react";
import { useA<PERSON> } from "@/contexts/AuthContext";
import { Navigate, useNavigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import StatsCards from "@/components/dashboard/StatsCards";
import DomainList from "@/components/dashboard/DomainList";
import RecentActivity from "@/components/dashboard/RecentActivity";
import { mockDomains, mockAnalytics, mockRecentActivity } from "@/data/mockData";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Plus, Filter, BarChart3, TrendingUp, <PERSON> } from "lucide-react";

const Dashboard = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col">
          <header className="h-12 flex items-center border-b px-6">
            <SidebarTrigger />
          </header>
          
          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
        {/* Quick Actions Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">Portfolio Overview</h1>
            <p className="text-muted-foreground">
              Monitor your domain portfolio and track performance
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={() => navigate("/dashboard/search")}>
              <Search className="mr-2 h-4 w-4" />
              Discover Domains
            </Button>
            <Button variant="outline" onClick={() => navigate("/dashboard/watchlist")}>
              <Eye className="mr-2 h-4 w-4" />
              Watchlist
            </Button>
            <Button variant="outline" onClick={() => navigate("/dashboard/trends")}>
              <TrendingUp className="mr-2 h-4 w-4" />
              Market Trends
            </Button>
            <Button className="bg-gradient-primary">
              <Plus className="mr-2 h-4 w-4" />
              Add Domain
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <StatsCards analytics={mockAnalytics} />

        {/* Main Content */}
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            {/* Search and Filters */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search domains, evaluate new ones..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Domain Portfolio */}
            <DomainList domains={mockDomains} />
          </div>

          <div className="space-y-8">
            {/* Recent Activity */}
            <RecentActivity activities={mockRecentActivity} />

            {/* Quick Stats */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Market Insights</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">AI domains trend</span>
                    <span className="text-sm font-medium text-green-600">+15.2%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Tech domains avg</span>
                    <span className="text-sm font-medium">$8.5K</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Success rate</span>
                    <span className="text-sm font-medium text-blue-600">92%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Tabs Section */}
        <Tabs defaultValue="portfolio" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
            <TabsTrigger value="discovery">Discovery</TabsTrigger>
            <TabsTrigger value="offers">Offers</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="portfolio" className="space-y-6">
            <div className="text-center py-8">
              <h3 className="text-lg font-semibold mb-2">Portfolio Overview</h3>
              <p className="text-muted-foreground">
                Your complete domain portfolio is displayed above. Use filters to find specific domains.
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="discovery" className="space-y-6">
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Domain Discovery</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-lg font-semibold mb-2">AI-Powered Domain Discovery</h3>
                  <p className="text-muted-foreground mb-4">
                    Discover valuable domains using our AI algorithms
                  </p>
                  <Button className="bg-gradient-primary">
                    Start Discovery
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="offers" className="space-y-6">
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Active Offers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-lg font-semibold mb-2">Manage Your Offers</h3>
                  <p className="text-muted-foreground">
                    You have {mockAnalytics.pendingOffers} pending offers to review
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics" className="space-y-6">
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Performance Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-lg font-semibold mb-2">Portfolio Analytics</h3>
                  <p className="text-muted-foreground">
                    Detailed analytics and performance metrics for your portfolio
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Dashboard;