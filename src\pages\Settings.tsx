import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Settings as SettingsIcon, User, Bell, CreditCard, Shield, Mail, Smartphone } from "lucide-react";

const Settings = () => {
  const { isAuthenticated, user } = useAuth();
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    priceAlerts: true,
    marketUpdates: true,
    domainExpiry: true
  });

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col">
          <header className="h-12 flex items-center border-b px-6">
            <SidebarTrigger />
          </header>
          
          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex items-center gap-3">
              <SettingsIcon className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold">Account Settings</h1>
                <p className="text-muted-foreground">
                  Manage your account preferences and settings
                </p>
              </div>
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              {/* Profile Settings */}
              <div className="lg:col-span-2 space-y-6">
                <Card className="border-0 shadow-soft">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Profile Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name</Label>
                        <Input id="firstName" defaultValue={user?.email?.split('@')[0] || "John"} />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input id="lastName" defaultValue="Doe" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input id="email" type="email" defaultValue={user?.email || "<EMAIL>"} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input id="phone" placeholder="+****************" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company">Company</Label>
                      <Input id="company" placeholder="Your company name" />
                    </div>
                    <Button className="bg-gradient-primary">
                      Save Changes
                    </Button>
                  </CardContent>
                </Card>

                {/* Notification Settings */}
                <Card className="border-0 shadow-soft">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="h-5 w-5" />
                      Notification Preferences
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            <Label>Email Notifications</Label>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Receive updates via email
                          </p>
                        </div>
                        <Switch 
                          checked={notifications.email}
                          onCheckedChange={(checked) => 
                            setNotifications(prev => ({ ...prev, email: checked }))
                          }
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Smartphone className="h-4 w-4" />
                            <Label>SMS Notifications</Label>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Receive urgent alerts via SMS
                          </p>
                        </div>
                        <Switch 
                          checked={notifications.sms}
                          onCheckedChange={(checked) => 
                            setNotifications(prev => ({ ...prev, sms: checked }))
                          }
                        />
                      </div>

                      <Separator />

                      <div className="space-y-3">
                        <Label className="text-base font-medium">Alert Types</Label>
                        
                        <div className="flex items-center justify-between">
                          <Label>Price Change Alerts</Label>
                          <Switch 
                            checked={notifications.priceAlerts}
                            onCheckedChange={(checked) => 
                              setNotifications(prev => ({ ...prev, priceAlerts: checked }))
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <Label>Market Updates</Label>
                          <Switch 
                            checked={notifications.marketUpdates}
                            onCheckedChange={(checked) => 
                              setNotifications(prev => ({ ...prev, marketUpdates: checked }))
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <Label>Domain Expiry Warnings</Label>
                          <Switch 
                            checked={notifications.domainExpiry}
                            onCheckedChange={(checked) => 
                              setNotifications(prev => ({ ...prev, domainExpiry: checked }))
                            }
                          />
                        </div>
                      </div>
                    </div>

                    <Button variant="outline">
                      Update Preferences
                    </Button>
                  </CardContent>
                </Card>

                {/* Security Settings */}
                <Card className="border-0 shadow-soft">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      Security & Privacy
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Change Password</Label>
                      <div className="space-y-3">
                        <Input type="password" placeholder="Current password" />
                        <Input type="password" placeholder="New password" />
                        <Input type="password" placeholder="Confirm new password" />
                      </div>
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Two-Factor Authentication</Label>
                        <p className="text-sm text-muted-foreground">
                          Add an extra layer of security
                        </p>
                      </div>
                      <Button variant="outline" size="sm">
                        Enable 2FA
                      </Button>
                    </div>
                    <Button variant="outline">
                      Update Security
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Current Plan */}
                <Card className="border-0 shadow-soft">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Current Plan
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-center space-y-2">
                      <Badge className="bg-gradient-primary text-primary-foreground">
                        Pro Plan
                      </Badge>
                      <div className="text-2xl font-bold">$15/month</div>
                      <p className="text-sm text-muted-foreground">
                        Unlimited evaluations & features
                      </p>
                    </div>
                    <Separator />
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Next billing date:</span>
                        <span>Jan 15, 2024</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Domains evaluated:</span>
                        <span>127 this month</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full">
                        Change Plan
                      </Button>
                      <Button variant="ghost" className="w-full" size="sm">
                        View Billing History
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card className="border-0 shadow-soft">
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      Export Portfolio Data
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Download Reports
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      API Documentation
                    </Button>
                    <Separator />
                    <Button variant="ghost" className="w-full justify-start text-red-600 hover:text-red-700">
                      Delete Account
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Settings;