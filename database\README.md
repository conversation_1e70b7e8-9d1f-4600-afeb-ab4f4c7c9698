# DomainSpot Database Schema

This directory contains the database schema and related files for the DomainSpot application using Supabase.

## Files

- `schema.sql` - Complete database schema with tables, indexes, RLS policies, and functions
- `README.md` - This documentation file

## Recent Updates

**2024-01-15**:

- ✅ Created comprehensive database schema with all necessary tables
- ✅ Added Row Level Security (RLS) policies for data protection
- ✅ Implemented messaging system with conversations and messages tables
- ✅ Added proper indexing for performance optimization
- ✅ Created helper functions for user management and conversations
- ✅ Added sample market trends data

## Database Structure

### Core Tables

1. **profiles** - User profiles (extends Supabase auth.users)
2. **domains** - User-owned domains with valuation and metadata
3. **watchlist** - Domains users are watching/interested in
4. **messages** - Direct messages between users
5. **conversations** - Message threads between users
6. **domain_offers** - Buy/sell offers for domains
7. **analytics_events** - User activity tracking
8. **market_trends** - Market data and trends
9. **search_history** - User search queries
10. **notifications** - User notifications

### Key Features

- **Row Level Security (RLS)** - All tables have appropriate RLS policies
- **Automatic timestamps** - `created_at` and `updated_at` fields with triggers
- **UUID primary keys** - Using uuid-ossp extension
- **Proper indexing** - Optimized for common queries
- **User authentication** - Integrated with Supabase Auth

## Setup Instructions

### 1. Create Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create a new project
3. Note your project URL and anon key

### 2. Run Schema

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `schema.sql`
4. Run the query to create all tables and policies

### 3. Configure Environment

Create a `.env.local` file in your project root:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Usage Examples

### User Profile Management

```sql
-- Get user profile
SELECT * FROM profiles WHERE id = auth.uid();

-- Update user profile
UPDATE profiles
SET name = 'New Name', plan = 'pro'
WHERE id = auth.uid();
```

### Domain Management

```sql
-- Add domain to portfolio
INSERT INTO domains (user_id, name, extension, purchase_price, category)
VALUES (auth.uid(), 'example', 'com', 1000.00, 'Tech');

-- Get user's domains
SELECT * FROM domains WHERE user_id = auth.uid() ORDER BY created_at DESC;
```

### Messaging

```sql
-- Send a message
INSERT INTO messages (sender_id, recipient_id, subject, content)
VALUES (auth.uid(), 'recipient_uuid', 'Domain Inquiry', 'Interested in your domain...');

-- Get conversations
SELECT c.*, p.name as other_user_name
FROM conversations c
JOIN profiles p ON (
  CASE
    WHEN c.participant_1_id = auth.uid() THEN c.participant_2_id
    ELSE c.participant_1_id
  END = p.id
)
WHERE c.participant_1_id = auth.uid() OR c.participant_2_id = auth.uid()
ORDER BY c.last_message_at DESC;
```

### Watchlist

```sql
-- Add domain to watchlist
INSERT INTO watchlist (user_id, domain_name, domain_extension, estimated_value)
VALUES (auth.uid(), 'premium', 'com', 5000.00);

-- Get watchlist
SELECT * FROM watchlist WHERE user_id = auth.uid() ORDER BY created_at DESC;
```

## Security Notes

- All tables use Row Level Security (RLS)
- Users can only access their own data
- Messages are only visible to sender and recipient
- Market trends are publicly readable
- Authentication is handled by Supabase Auth

## Maintenance

### Regular Tasks

1. **Monitor performance** - Check slow queries and add indexes as needed
2. **Clean old data** - Archive old analytics events and search history
3. **Update market trends** - Regular updates to market_trends table
4. **Backup data** - Regular database backups

### Schema Updates

When updating the schema:

1. Test changes in development environment
2. Create migration scripts for production
3. Update this documentation
4. Notify team of breaking changes

## API Integration

The schema is designed to work with:

- Supabase JavaScript client
- Real-time subscriptions
- Supabase Auth
- Row Level Security policies

## Support

For questions about the database schema:

1. Check this documentation
2. Review the schema.sql file
3. Consult Supabase documentation
4. Contact the development team
