import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, X } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useNavigate } from "react-router-dom";

const Pricing = () => {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "sixmonth">("monthly");
  const navigate = useNavigate();

  const plans = [
    {
      name: "Free",
      price: 0,
      description: "Perfect for getting started with domain investing",
      features: [
        "5 domain evaluations per month",
        "Basic AI scoring",
        "Domain watchlist (10 domains)",
        "Email notifications",
        "Basic market insights",
      ],
      limitations: [
        "No buyer contact tools",
        "No advanced analytics",
        "No bulk operations",
        "Limited support"
      ],
      popular: false,
      buttonText: "Get Started Free",
      buttonVariant: "outline" as const
    },
    {
      name: "Pro",
      price: 15,
      description: "For serious domain investors and professionals",
      features: [
        "Unlimited domain evaluations",
        "Advanced AI scoring & insights",
        "Unlimited watchlist",
        "Buyer contact database access",
        "Automated outreach campaigns",
        "Portfolio analytics",
        "Market trend analysis",
        "Priority support",
        "Bulk domain operations",
        "Custom alerts & notifications"
      ],
      limitations: [],
      popular: true,
      buttonText: "Start Pro Trial",
      buttonVariant: "default" as const
    },
    {
      name: "6-Month Plan",
      price: 90,
      originalPrice: 90,
      description: "Best value - save 50% with our 6-month commitment",
      features: [
        "Everything in Pro",
        "50% discount vs monthly",
        "6 months of full access",
        "Priority customer support",
        "Advanced portfolio analytics",
        "Market trend predictions",
        "Bulk export capabilities",
        "Premium buyer database",
        "Custom alert settings",
        "Advanced search filters"
      ],
      limitations: [],
      popular: false,
      buttonText: "Get 6-Month Plan",
      buttonVariant: "outline" as const
    }
  ];

  const handlePlanSelect = (planName: string) => {
    if (planName === "Free") {
      navigate("/auth");
    } else if (planName === "Enterprise") {
      // Handle contact sales
      console.log("Contact sales for Enterprise plan");
    } else {
      navigate("/auth");
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-6">
          {/* Header Section */}
          <div className="text-center mb-16 space-y-4">
            <Badge className="bg-gradient-primary text-primary-foreground mb-4">
              Pricing Plans
            </Badge>
            <h1 className="text-4xl lg:text-5xl font-bold">
              Choose Your <span className="bg-gradient-primary bg-clip-text text-transparent">Investment</span> Plan
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Start free, scale as you grow. All plans include our core features to help you spot, evaluate, acquire, and resell domains.
            </p>
          </div>


          {/* Pricing Cards */}
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan) => (
              <Card
                key={plan.name}
                className={`relative border-0 shadow-soft ${
                  plan.popular ? "ring-2 ring-primary scale-105" : ""
                }`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-primary text-primary-foreground">
                    Most Popular
                  </Badge>
                )}
                
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                  <div className="space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-4xl font-bold">
                        ${plan.price}
                      </span>
                      {plan.name === "Pro" && (
                        <span className="text-muted-foreground">
                          /month
                        </span>
                      )}
                      {plan.name === "6-Month Plan" && (
                        <span className="text-muted-foreground">
                          /6 months
                        </span>
                      )}
                    </div>
                    {plan.originalPrice && plan.name === "6-Month Plan" && (
                      <div className="text-sm text-muted-foreground">
                        Save $45 vs monthly plan
                      </div>
                    )}
                  </div>
                  <p className="text-muted-foreground text-sm">{plan.description}</p>
                </CardHeader>

                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                    {plan.limitations.map((limitation, index) => (
                      <div key={index} className="flex items-start gap-3 opacity-60">
                        <X className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{limitation}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    className={`w-full ${
                      plan.buttonVariant === "default" ? "bg-gradient-primary" : ""
                    }`}
                    variant={plan.buttonVariant}
                    onClick={() => handlePlanSelect(plan.name)}
                  >
                    {plan.buttonText}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* FAQ Section */}
          <div className="mt-20 text-center">
            <h3 className="text-2xl font-bold mb-8">Frequently Asked Questions</h3>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left">
              <div className="space-y-3">
                <h4 className="font-semibold">Can I upgrade or downgrade anytime?</h4>
                <p className="text-muted-foreground text-sm">
                  Yes, you can change your plan at any time. Changes take effect immediately and we'll prorate any differences.
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold">What's included in the free trial?</h4>
                <p className="text-muted-foreground text-sm">
                  The Pro trial includes all Pro features for 14 days. No credit card required to get started.
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold">How accurate is the AI evaluation?</h4>
                <p className="text-muted-foreground text-sm">
                  Our AI has a 98% accuracy rate, analyzing over 200 factors including market trends, keyword value, and historical sales data.
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold">Do you offer refunds?</h4>
                <p className="text-muted-foreground text-sm">
                  Yes, we offer a 30-day money-back guarantee on all paid plans. Contact support for assistance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Pricing;