import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  MessageSquare,
  Send,
  Search,
  Plus,
  MoreVertical,
  Reply,
  Forward,
  Archive,
  Trash2,
  Star,
  Clock,
  User
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Messages = () => {
  const { isAuthenticated, user } = useAuth();
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [showCompose, setShowCompose] = useState(false);
  const [composeRecipient, setComposeRecipient] = useState("");
  const [composeSubject, setComposeSubject] = useState("");
  const [composeMessage, setComposeMessage] = useState("");

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // Mock data - in real app, this would come from Supabase
  const conversations = [
    {
      id: "1",
      participant: {
        id: "user1",
        name: "John Smith",
        avatar: "",
        email: "<EMAIL>"
      },
      lastMessage: {
        content: "I'm interested in your domain techstartup.ai. What's your asking price?",
        timestamp: "2024-01-15T10:30:00Z",
        isRead: false
      },
      unreadCount: 2,
      relatedDomain: "techstartup.ai"
    },
    {
      id: "2",
      participant: {
        id: "user2",
        name: "Sarah Johnson",
        avatar: "",
        email: "<EMAIL>"
      },
      lastMessage: {
        content: "Thanks for the domain evaluation. The AI score looks great!",
        timestamp: "2024-01-14T15:45:00Z",
        isRead: true
      },
      unreadCount: 0,
      relatedDomain: null
    },
    {
      id: "3",
      participant: {
        id: "user3",
        name: "Mike Chen",
        avatar: "",
        email: "<EMAIL>"
      },
      lastMessage: {
        content: "Would you consider a counter-offer of $15,000 for blockchain-tools.com?",
        timestamp: "2024-01-13T09:20:00Z",
        isRead: true
      },
      unreadCount: 0,
      relatedDomain: "blockchain-tools.com"
    }
  ];

  const messages = [
    {
      id: "1",
      senderId: "user1",
      senderName: "John Smith",
      content: "Hi! I saw your domain techstartup.ai listed. I'm very interested in purchasing it.",
      timestamp: "2024-01-15T09:15:00Z",
      isRead: true
    },
    {
      id: "2",
      senderId: user?.id || "current-user",
      senderName: user?.name || "You",
      content: "Hello John! Thanks for your interest. The domain is indeed available. What kind of project are you planning to use it for?",
      timestamp: "2024-01-15T09:45:00Z",
      isRead: true
    },
    {
      id: "3",
      senderId: "user1",
      senderName: "John Smith",
      content: "I'm building an AI-powered startup platform. The domain would be perfect for our brand. What's your asking price?",
      timestamp: "2024-01-15T10:30:00Z",
      isRead: false
    }
  ];

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      // In real app, send message via Supabase
      console.log("Sending message:", newMessage);
      setNewMessage("");
    }
  };

  const handleComposeMessage = () => {
    if (composeMessage.trim() && composeRecipient.trim()) {
      // In real app, create new conversation and send message via Supabase
      console.log("Composing message:", { composeRecipient, composeSubject, composeMessage });
      setShowCompose(false);
      setComposeRecipient("");
      setComposeSubject("");
      setComposeMessage("");
    }
  };

  const selectedConv = conversations.find(c => c.id === selectedConversation);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <header className="h-16 flex items-center justify-between border-b px-6 bg-card/50 backdrop-blur-sm">
            <div className="flex items-center gap-4">
              <SidebarTrigger />
              <div className="hidden md:block">
                <h2 className="text-lg font-semibold text-foreground">Messages</h2>
                <p className="text-sm text-muted-foreground">Connect with other domain investors</p>
              </div>
            </div>
          </header>

          <main className="flex-1 flex">
            {/* Conversations List */}
            <div className="w-80 border-r bg-card flex flex-col">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Messages
                  </h2>
                  <Button
                    size="sm"
                    onClick={() => setShowCompose(true)}
                    className="bg-gradient-primary"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search conversations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <ScrollArea className="flex-1">
                <div className="p-2">
                  {conversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 ${selectedConversation === conversation.id
                          ? "bg-primary/10 border border-primary/20"
                          : "hover:bg-muted/50"
                        }`}
                      onClick={() => setSelectedConversation(conversation.id)}
                    >
                      <div className="flex items-start gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={conversation.participant.avatar} />
                          <AvatarFallback>
                            {conversation.participant.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="font-medium truncate">
                              {conversation.participant.name}
                            </p>
                            <div className="flex items-center gap-1">
                              {conversation.unreadCount > 0 && (
                                <Badge variant="default" className="h-5 w-5 p-0 text-xs">
                                  {conversation.unreadCount}
                                </Badge>
                              )}
                              <span className="text-xs text-muted-foreground">
                                {formatTime(conversation.lastMessage.timestamp)}
                              </span>
                            </div>
                          </div>

                          <p className="text-sm text-muted-foreground truncate mt-1">
                            {conversation.lastMessage.content}
                          </p>

                          {conversation.relatedDomain && (
                            <Badge variant="outline" className="mt-2 text-xs">
                              {conversation.relatedDomain}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Message View */}
            <div className="flex-1 flex flex-col">
              {selectedConv ? (
                <>
                  {/* Conversation Header */}
                  <div className="p-4 border-b bg-card">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={selectedConv.participant.avatar} />
                          <AvatarFallback>
                            {selectedConv.participant.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold">{selectedConv.participant.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {selectedConv.participant.email}
                          </p>
                        </div>
                        {selectedConv.relatedDomain && (
                          <Badge variant="outline">
                            Re: {selectedConv.relatedDomain}
                          </Badge>
                        )}
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Star className="mr-2 h-4 w-4" />
                            Star Conversation
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Archive className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  {/* Messages */}
                  <ScrollArea className="flex-1 p-4">
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex gap-3 ${message.senderId === user?.id ? "justify-end" : "justify-start"
                            }`}
                        >
                          {message.senderId !== user?.id && (
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="text-xs">
                                {message.senderName.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                          )}

                          <div
                            className={`max-w-[70%] rounded-lg p-3 ${message.senderId === user?.id
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted"
                              }`}
                          >
                            <p className="text-sm">{message.content}</p>
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs opacity-70">
                                {formatTime(message.timestamp)}
                              </span>
                              {message.senderId === user?.id && (
                                <span className="text-xs opacity-70">
                                  {message.isRead ? "Read" : "Sent"}
                                </span>
                              )}
                            </div>
                          </div>

                          {message.senderId === user?.id && (
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="text-xs">
                                {message.senderName.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>

                  {/* Message Input */}
                  <div className="p-4 border-t bg-card">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                        className="flex-1"
                      />
                      <Button onClick={handleSendMessage} className="bg-gradient-primary">
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <MessageSquare className="h-16 w-16 text-muted-foreground mx-auto" />
                    <div>
                      <h3 className="text-lg font-semibold">No conversation selected</h3>
                      <p className="text-muted-foreground">
                        Choose a conversation from the list to start messaging
                      </p>
                    </div>
                    <Button onClick={() => setShowCompose(true)} className="bg-gradient-primary">
                      <Plus className="mr-2 h-4 w-4" />
                      Start New Conversation
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </main>
        </div>
      </div>

      {/* Compose Message Modal */}
      {showCompose && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>New Message</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">To:</label>
                <Input
                  placeholder="Enter email or username"
                  value={composeRecipient}
                  onChange={(e) => setComposeRecipient(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Subject:</label>
                <Input
                  placeholder="Message subject (optional)"
                  value={composeSubject}
                  onChange={(e) => setComposeSubject(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Message:</label>
                <Textarea
                  placeholder="Type your message..."
                  value={composeMessage}
                  onChange={(e) => setComposeMessage(e.target.value)}
                  rows={4}
                />
              </div>
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setShowCompose(false)}>
                  Cancel
                </Button>
                <Button onClick={handleComposeMessage} className="bg-gradient-primary">
                  <Send className="mr-2 h-4 w-4" />
                  Send Message
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </SidebarProvider>
  );
};

export default Messages;
