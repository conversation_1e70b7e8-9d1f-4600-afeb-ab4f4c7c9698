import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Eye, Search, Plus, Bell, Star, TrendingUp, AlertCircle } from "lucide-react";

const Watchlist = () => {
  const { isAuthenticated } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const watchlistDomains = [
    {
      domain: "smartai.com",
      status: "available",
      currentPrice: "$25,000",
      targetPrice: "$20,000",
      lastChecked: "2 hours ago",
      alerts: 3,
      trending: true,
      score: 95
    },
    {
      domain: "voicetech.io",
      status: "auction",
      currentPrice: "$8,500",
      targetPrice: "$10,000",
      lastChecked: "1 hour ago",
      alerts: 1,
      trending: false,
      score: 88
    },
    {
      domain: "blockchain-hub.com",
      status: "sold",
      currentPrice: "$15,000",
      targetPrice: "$12,000",
      lastChecked: "1 day ago",
      alerts: 0,
      trending: false,
      score: 92
    },
    {
      domain: "cybersec.ai",
      status: "watching",
      currentPrice: "$5,200",
      targetPrice: "$8,000",
      lastChecked: "30 minutes ago",
      alerts: 2,
      trending: true,
      score: 90
    },
    {
      domain: "dataflow.tech",
      status: "available",
      currentPrice: "$18,000",
      targetPrice: "$15,000",
      lastChecked: "4 hours ago",
      alerts: 1,
      trending: false,
      score: 85
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available": return "bg-green-100 text-green-800 border-green-200";
      case "auction": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "sold": return "bg-red-100 text-red-800 border-red-200";
      case "watching": return "bg-blue-100 text-blue-800 border-blue-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <header className="h-16 flex items-center justify-between border-b px-6 bg-card/50 backdrop-blur-sm">
            <div className="flex items-center gap-4">
              <SidebarTrigger />
              <div className="hidden md:block">
                <h2 className="text-lg font-semibold text-foreground">Watchlist</h2>
                <p className="text-sm text-muted-foreground">Track domains you're interested in</p>
              </div>
            </div>
          </header>

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-2">
                  <Eye className="h-8 w-8 text-primary" />
                  Domain Watchlist
                </h1>
                <p className="text-muted-foreground">
                  Track domains you're interested in and get notified of price changes
                </p>
              </div>
              <Button className="bg-gradient-primary">
                <Plus className="mr-2 h-4 w-4" />
                Add Domain
              </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid md:grid-cols-4 gap-6">
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Watched</p>
                      <p className="text-2xl font-bold">{watchlistDomains.length}</p>
                    </div>
                    <Eye className="h-8 w-8 text-primary" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Active Alerts</p>
                      <p className="text-2xl font-bold">
                        {watchlistDomains.reduce((sum, domain) => sum + domain.alerts, 0)}
                      </p>
                    </div>
                    <Bell className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Available Now</p>
                      <p className="text-2xl font-bold">
                        {watchlistDomains.filter(d => d.status === "available").length}
                      </p>
                    </div>
                    <AlertCircle className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Trending</p>
                      <p className="text-2xl font-bold">
                        {watchlistDomains.filter(d => d.trending).length}
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Search */}
            <Card className="border-0 shadow-soft">
              <CardContent className="p-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search watchlist domains..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Watchlist */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Your Watchlist</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {watchlistDomains.map((domain, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-lg border bg-card hover:shadow-sm transition-all">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          {domain.trending && <TrendingUp className="h-4 w-4 text-blue-600" />}
                          <Star className="h-4 w-4 text-yellow-500" />
                        </div>
                        <div>
                          <h3 className="font-semibold">{domain.domain}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={getStatusColor(domain.status)} variant="outline">
                              {domain.status}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              Score: {domain.score}/100
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-6">
                        <div className="text-right">
                          <p className="font-bold">{domain.currentPrice}</p>
                          <p className="text-sm text-muted-foreground">
                            Target: {domain.targetPrice}
                          </p>
                        </div>

                        <div className="text-right">
                          <div className="flex items-center gap-1">
                            <Bell className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{domain.alerts}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {domain.lastChecked}
                          </p>
                        </div>

                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                          <Button variant="ghost" size="sm">
                            Remove
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Watchlist;